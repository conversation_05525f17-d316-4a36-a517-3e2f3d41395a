#!/bin/bash

# AdMesh Protocol - Test Environment Runner
# Uses production Firebase/database but exposes on localhost for testing

echo "🧪 Starting AdMesh Protocol in TEST mode..."
echo "📊 Database: Production (admesh-9560c)"
echo "🌐 API: http://127.0.0.1:8000"
echo "🔧 Environment: test"
echo ""

# Check if production Firebase credentials exist
if [ ! -f "./firebase/serviceAccountKey.json" ]; then
    echo "❌ Error: Production Firebase credentials not found!"
    echo "   Please ensure ./firebase/serviceAccountKey.json exists"
    exit 1
fi

# Load test environment variables
if [ -f ".env.test" ]; then
    echo "📋 Loading test environment variables..."
    export $(cat .env.test | grep -v '^#' | xargs)
else
    echo "❌ Error: .env.test file not found!"
    exit 1
fi

# Verify we're using production Firebase credentials
echo "🔍 Verifying Firebase configuration..."
echo "   Credentials file: ./firebase/serviceAccountKey.json"
if grep -q "admesh-9560c" "./firebase/serviceAccountKey.json"; then
    echo "   ✅ Production Firebase project (admesh-9560c) confirmed"
else
    echo "   ❌ Error: serviceAccountKey.json does not contain production project ID"
    exit 1
fi

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "🐍 Activating virtual environment..."
    source venv/bin/activate
elif [ -d "testenv" ]; then
    echo "🐍 Activating test virtual environment..."
    source testenv/bin/activate
else
    echo "⚠️  Warning: No virtual environment found. Using system Python."
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."
pip install -r requirements.txt > /dev/null 2>&1

# Set environment to test (ensure it's set)
export ENV=test
export ENVIRONMENT=test

# Bind to localhost only for test environment
export HOST=127.0.0.1

# Ensure port is set
if [ -z "$PORT" ]; then
    export PORT=8000
fi

echo "🚀 Starting FastAPI server..."
echo "   Environment: $ENV"
echo "   Host: $HOST:$PORT"
echo "   Firebase Project: admesh-9560c (production)"
echo "   Firebase Credentials: ./firebase/serviceAccountKey.json"
echo "   API URL: http://127.0.0.1:8000"
echo ""
echo "📝 Logs will show below..."
echo "   Press Ctrl+C to stop the server"
echo "   To run dashboard: cd ../admesh-dashboard && npm run dev:test"
echo ""

# Start the server with uvicorn
uvicorn api.main:app \
    --host $HOST \
    --port $PORT \
    --reload \
    --log-level debug \
    --access-log \
    --env-file .env.test

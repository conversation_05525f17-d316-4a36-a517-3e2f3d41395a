# Test Environment Configuration for AdMesh Dashboard
# Uses production Firebase but calls localhost API

# Environment
NEXT_PUBLIC_ENVIRONMENT=test
NODE_ENV=development

# Firebase Configuration (production)
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDC7cun2Klpj_NdyGGAJz0FGezjeS7dtQM
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=admesh-9560c.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=admesh-9560c
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=admesh-9560c.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=857884660055
NEXT_PUBLIC_FIREBASE_APP_ID=1:857884660055:web:4f7cbe97e3e62b794bee12

# API Configuration (localhost for testing)
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000

# External Services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here

# Test Configuration
NEXT_PUBLIC_AGENT_ID=ZAinjf9SuPYCk7u8r0ZEeKY2fV42
NEXT_PUBLIC_USER_ID=C8SMDERS3naFG2k103EDBacoTCy2
NEXT_PUBLIC_AGENT_API_KEY=sk_test_IFTLcrkWf2Hx9GUfb6pNXwaMpJ4GryRw

# Analytics (disabled for test)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NEXT_PUBLIC_ENABLE_ANALYTICS=false

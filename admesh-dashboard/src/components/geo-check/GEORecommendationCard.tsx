import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertTriangle,
  CheckCircle,
  Info,
  ExternalLink,
  BookOpen
} from "lucide-react";

interface GEORecommendation {
  priority: "high" | "medium" | "low";
  category: string;
  title: string;
  description: string;
  impact: string;
}

interface GEORecommendationCardProps {
  recommendation: GEORecommendation;
  onLearnMore?: () => void;
}

export function GEORecommendationCard({ 
  recommendation, 
  onLearnMore 
}: GEORecommendationCardProps) {
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "medium":
        return <Info className="h-4 w-4 text-yellow-600" />;
      case "low":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "outline";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "border-l-red-500";
      case "medium":
        return "border-l-yellow-500";
      case "low":
        return "border-l-green-500";
      default:
        return "border-l-gray-500";
    }
  };

  return (
    <Card className={`bg-white border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-200 border-l-4 ${getPriorityColor(recommendation.priority)}`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${
              recommendation.priority === 'high' ? 'bg-red-50' :
              recommendation.priority === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              {getPriorityIcon(recommendation.priority)}
            </div>
            <CardTitle className="text-lg font-semibold text-gray-900">{recommendation.title}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700 border-gray-200">
              {recommendation.category}
            </Badge>
            <Badge
              variant={getPriorityBadgeVariant(recommendation.priority)}
              className={`text-xs font-medium ${
                recommendation.priority === 'high' ? 'bg-red-100 text-red-800 border-red-200' :
                recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                'bg-green-100 text-green-800 border-green-200'
              }`}
            >
              {recommendation.priority.toUpperCase()}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600 leading-relaxed">
          {recommendation.description}
        </p>

        <div className="bg-gray-50 border border-gray-100 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xs font-medium text-gray-700 uppercase tracking-wide">Expected Impact:</span>
          </div>
          <p className="text-sm text-gray-900 font-medium">{recommendation.impact}</p>
        </div>

        <div className="flex items-center gap-3 pt-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200"
            onClick={onLearnMore}
          >
            <BookOpen className="h-3 w-3" />
            Learn More
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="gap-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200"
            onClick={() => {
              // Open GEO best practices guide
              window.open("https://docs.useadmesh.com/geo-optimization", "_blank");
            }}
          >
            <ExternalLink className="h-3 w-3" />
            GEO Guide
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface GEOScoreCardProps {
  title: string;
  score: number;
  maxScore?: number;
  description: string;
  trend?: "up" | "down" | "stable";
}

export function GEOScoreCard({ 
  title, 
  score, 
  maxScore = 100, 
  description, 
  trend 
}: GEOScoreCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    return "Needs Improvement";
  };

  return (
    <Card className="bg-white border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-200 group">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
            {title}
          </CardTitle>
          <div className={`w-3 h-3 rounded-full ${
            score >= 80 ? 'bg-green-500' :
            score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
          }`} />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-3xl font-bold">
              <span className={getScoreColor(score)}>{score}</span>
              <span className="text-gray-400 text-lg">/{maxScore}</span>
            </div>
            <Badge
              variant={getScoreBadgeVariant(score)}
              className={`${
                score >= 80 ? 'bg-green-100 text-green-800 border-green-200' :
                score >= 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                'bg-red-100 text-red-800 border-red-200'
              }`}
            >
              {getScoreLabel(score)}
            </Badge>
          </div>

          <div className="space-y-2">
            <Progress
              value={(score / maxScore) * 100}
              className={`h-2 ${
                score >= 80 ? '[&>div]:bg-green-500' :
                score >= 60 ? '[&>div]:bg-yellow-500' :
                '[&>div]:bg-red-500'
              }`}
            />
            <div className="flex items-center gap-1">
              <div className={`h-1 w-8 rounded-full ${
                score >= 80 ? 'bg-gradient-to-r from-green-400 to-emerald-500' :
                score >= 60 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                'bg-gradient-to-r from-red-400 to-pink-500'
              }`}></div>
              <span className="text-xs text-gray-500">{description}</span>
            </div>
          </div>

          {trend && (
            <div className="flex items-center gap-2 text-xs pt-2 border-t border-gray-100">
              {trend === "up" && (
                <>
                  <div className="flex items-center gap-1 text-green-600">
                    <span className="text-sm">↗</span>
                    <span className="font-medium">Improving</span>
                  </div>
                </>
              )}
              {trend === "down" && (
                <>
                  <div className="flex items-center gap-1 text-red-600">
                    <span className="text-sm">↘</span>
                    <span className="font-medium">Declining</span>
                  </div>
                </>
              )}
              {trend === "stable" && (
                <>
                  <div className="flex items-center gap-1 text-gray-600">
                    <span className="text-sm">→</span>
                    <span className="font-medium">Stable</span>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* Enhanced black and white compliant color scheme */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.09 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.09 0 0);
  --primary: oklch(0.09 0 0);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.96 0 0);
  --secondary-foreground: oklch(0.09 0 0);
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.45 0 0);
  --accent: oklch(0.96 0 0);
  --accent-foreground: oklch(0.09 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.89 0 0);
  --input: oklch(0.89 0 0);
  --ring: oklch(0.45 0 0);

  /* Professional chart colors with good contrast */
  --chart-1: oklch(0.45 0.15 220);
  --chart-2: oklch(0.55 0.12 160);
  --chart-3: oklch(0.35 0.08 280);
  --chart-4: oklch(0.65 0.18 40);
  --chart-5: oklch(0.50 0.15 320);

  /* Sidebar with professional styling */
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.09 0 0);
  --sidebar-primary: oklch(0.09 0 0);
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.94 0 0);
  --sidebar-accent-foreground: oklch(0.09 0 0);
  --sidebar-border: oklch(0.89 0 0);
  --sidebar-ring: oklch(0.45 0 0);

  /* Main background and foreground */
  --background: oklch(1 0 0);
  --foreground: oklch(0.09 0 0);

  /* AdMesh brand accent colors for highlights */
  --admesh-blue: oklch(0.45 0.15 220);
  --admesh-purple: oklch(0.50 0.15 280);
  --admesh-green: oklch(0.55 0.12 140);
  --admesh-yellow: oklch(0.65 0.18 80);
  --admesh-red: oklch(0.55 0.20 20);
}

.dark {
  /* Enhanced dark mode with professional black and white design */
  --background: oklch(0.09 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.12 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.12 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.95 0 0);
  --primary-foreground: oklch(0.09 0 0);
  --secondary: oklch(0.18 0 0);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.18 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.18 0 0);
  --accent-foreground: oklch(0.95 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.25 0 0);
  --input: oklch(0.25 0 0);
  --ring: oklch(0.65 0 0);

  /* Professional dark mode chart colors */
  --chart-1: oklch(0.65 0.15 220);
  --chart-2: oklch(0.70 0.12 160);
  --chart-3: oklch(0.60 0.08 280);
  --chart-4: oklch(0.75 0.18 40);
  --chart-5: oklch(0.68 0.15 320);

  /* Dark sidebar styling */
  --sidebar: oklch(0.12 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.65 0.15 220);
  --sidebar-primary-foreground: oklch(0.95 0 0);
  --sidebar-accent: oklch(0.18 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.25 0 0);
  --sidebar-ring: oklch(0.65 0 0);

  /* Dark mode AdMesh brand colors */
  --admesh-blue: oklch(0.65 0.15 220);
  --admesh-purple: oklch(0.70 0.15 280);
  --admesh-green: oklch(0.70 0.12 140);
  --admesh-yellow: oklch(0.75 0.18 80);
  --admesh-red: oklch(0.70 0.20 20);
}

@layer base {
  * {
    @apply border-border outline-ring/50 antialiased transition-colors duration-200;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans tracking-tight leading-relaxed;
  }

  h1, h2, h3, h4 {
    @apply font-semibold text-foreground;
  }

  p {
    @apply text-muted-foreground;
  }
}
@layer components {
  /* Enhanced card components for professional design */
  .card-glass {
    @apply bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border border-border shadow-sm rounded-lg;
  }

  .card-elevated {
    @apply bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-lg rounded-xl;
  }

  .card-interactive {
    @apply bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 hover:shadow-lg transition-all duration-200 cursor-pointer rounded-xl;
  }

  /* Professional text styling */
  .text-brand {
    @apply text-gray-900 dark:text-gray-100 font-semibold;
  }

  .text-muted-brand {
    @apply text-gray-600 dark:text-gray-400;
  }

  .text-subtle {
    @apply text-gray-500 dark:text-gray-500;
  }

  /* Enhanced input styling */
  .input-like {
    @apply px-4 py-2 text-sm rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-sm focus:ring-2 focus:ring-gray-900 dark:focus:ring-gray-100 focus:border-transparent;
  }

  /* Professional button styling */
  .btn-rounded {
    @apply px-4 py-2 rounded-full text-sm font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-200 font-medium rounded-lg px-4 py-2 transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700 font-medium rounded-lg px-4 py-2 transition-all duration-200;
  }

  /* Layout utilities */
  .section {
    @apply max-w-7xl px-4 sm:px-6 lg:px-8 mx-auto;
  }

  .container-brand {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* AdMesh branding elements */
  .admesh-gradient {
    @apply bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300;
  }

  .admesh-accent {
    @apply text-gray-900 dark:text-gray-100;
  }

  .admesh-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }

  /* Mobile-first responsive utilities */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .mobile-card {
    @apply p-4 sm:p-6;
  }

  .mobile-text {
    @apply text-sm sm:text-base;
  }

  .mobile-heading {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .mobile-button {
    @apply px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base;
  }

  .mobile-spacing {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  /* Dashboard specific responsive classes */
  .dashboard-header {
    @apply px-4 py-6 sm:px-6 sm:py-8 lg:px-8;
  }

  .dashboard-content {
    @apply px-4 py-6 sm:px-6 sm:py-8 lg:px-8;
  }

  .dashboard-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6;
  }

  .dashboard-card {
    @apply p-4 sm:p-6 rounded-lg sm:rounded-xl;
  }
}
@layer base {
  * {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-duration: 200ms;
    transition-timing-function: ease-in-out;
  }
}
.hero-bg {
  position: absolute;
  z-index: -1;
  top: 0;
  left: 50%;
  width: 80vw;
  height: 80vw;
  transform: translateX(-50%);
  background: radial-gradient(circle at 30% 30%, #6366f1, transparent 60%),
              radial-gradient(circle at 70% 70%, #ec4899, transparent 60%);
  opacity: 0.25;
  filter: blur(120px);
  animation: floatBlobs 20s ease-in-out infinite;
}

@keyframes floatBlobs {
  0%, 100% {
    transform: translate(-50%, 0);
  }
  50% {
    transform: translate(-50%, -40px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes float {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px);
    opacity: 0;
  }
}

.animate-float {
  animation: float 2s ease-out forwards;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
@layer base {
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

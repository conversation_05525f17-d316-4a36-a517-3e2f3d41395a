/**
 * Environment configuration for AdMesh Dashboard
 * Handles environment-specific settings for Firebase, API endpoints, and other services
 */

export interface EnvironmentConfig {
  environment: string;
  firebase: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
  };
  api: {
    baseUrl: string;
    timeout: number;
  };
  features: {
    analytics: boolean;
    debugging: boolean;
    errorReporting: boolean;
  };
  external: {
    stripePublishableKey?: string;
  };
}

// Development configuration
const developmentConfig: EnvironmentConfig = {
  environment: 'development',
  firebase: {
    apiKey: "AIzaSyDxBNmjuoMjkS5u8iad6PSB_5Lm7ggIkfY",
    authDomain: "admesh-dev.firebaseapp.com",
    projectId: "admesh-dev",
    storageBucket: "admesh-dev.firebasestorage.app",
    messagingSenderId: "651813374456",
    appId: "1:651813374456:web:dfc618425534b042576e0d"
  },
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000',
    timeout: 30000
  },
  features: {
    analytics: false,
    debugging: true,
    errorReporting: false
  },
  external: {
    stripePublishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  }
};



// Test configuration (production Firebase with local API)
const testConfig: EnvironmentConfig = {
  environment: 'test',
  firebase: {
    apiKey: "AIzaSyDC7cun2Klpj_NdyGGAJz0FGezjeS7dtQM",
    authDomain: "admesh-9560c.firebaseapp.com",
    projectId: "admesh-9560c",
    storageBucket: "admesh-9560c.firebasestorage.app",
    messagingSenderId: "857884660055",
    appId: "1:857884660055:web:4f7cbe97e3e62b794bee12"
  },
  api: {
    baseUrl: 'http://127.0.0.1:8000',
    timeout: 30000
  },
  features: {
    analytics: true,
    debugging: true,
    errorReporting: false
  },
  external: {
    stripePublishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  }
};

// Production configuration
const productionConfig: EnvironmentConfig = {
  environment: 'production',
  firebase: {
    apiKey: "AIzaSyDC7cun2Klpj_NdyGGAJz0FGezjeS7dtQM",
    authDomain: "admesh-9560c.firebaseapp.com",
    projectId: "admesh-9560c",
    storageBucket: "admesh-9560c.firebasestorage.app",
    messagingSenderId: "857884660055",
    appId: "1:857884660055:web:4f7cbe97e3e62b794bee12"
  },
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com',
    timeout: 30000
  },
  features: {
    analytics: true,
    debugging: false,
    errorReporting: true
  },
  external: {
    stripePublishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  }
};

/**
 * Detect the current environment
 */
function detectEnvironment(): string {
  // Check for explicit environment setting
  if (process.env.NEXT_PUBLIC_ENVIRONMENT) {
    return process.env.NEXT_PUBLIC_ENVIRONMENT.toLowerCase();
  }

  // Check for Vercel environment
  if (process.env.VERCEL_ENV) {
    switch (process.env.VERCEL_ENV) {
      case 'production':
        return 'production';
      default:
        return 'development';
    }
  }

  // Check for NODE_ENV
  if (process.env.NODE_ENV === 'production') {
    return 'production';
  }

  // Default to development
  return 'development';
}

/**
 * Get the configuration for the current environment
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const environment = detectEnvironment();

  switch (environment) {
    case 'production':
      return productionConfig;
    case 'test':
      return testConfig;
    case 'development':
    default:
      return developmentConfig;
  }
}

/**
 * Get the current environment name
 */
export function getCurrentEnvironment(): string {
  return detectEnvironment();
}

/**
 * Check if we're in development mode
 */
export function isDevelopment(): boolean {
  return detectEnvironment() === 'development';
}

/**
 * Check if we're in test mode
 */
export function isTest(): boolean {
  return detectEnvironment() === 'test';
}

/**
 * Check if we're in production mode
 */
export function isProduction(): boolean {
  return detectEnvironment() === 'production';
}



// Export the current configuration
export const config = getEnvironmentConfig();

// Log environment information (in development and test modes)
if (typeof window !== 'undefined' && (isDevelopment() || isTest())) {
  console.log('🔧 AdMesh Dashboard Environment:', config.environment);
  console.log('📦 Firebase Project:', config.firebase.projectId);
  console.log('🌐 API Base URL:', config.api.baseUrl);
}
